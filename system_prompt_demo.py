#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统提示词演示
展示不同系统提示词对AI回答的影响
"""

import os
import json
import requests
from pathlib import Path


def load_env_file(env_path=".env"):
    """加载.env文件中的环境变量"""
    env_file = Path(env_path)
    if not env_file.exists():
        print(f"⚠️  未找到 {env_path} 文件，将使用系统环境变量")
        return
    
    print(f"📁 正在加载配置文件: {env_path}")
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # 跳过空行和注释行
                if not line or line.startswith('#'):
                    continue
                
                # 解析键值对
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # 移除引号
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    
                    # 设置环境变量（如果系统环境变量中没有的话）
                    if key not in os.environ:
                        os.environ[key] = value
                        print(f"  ✅ 加载: {key}")
                    else:
                        print(f"  ⚠️  跳过: {key} (系统环境变量已存在)")
                else:
                    print(f"  ❌ 第{line_num}行格式错误: {line}")
    
    except Exception as e:
        print(f"❌ 加载.env文件失败: {str(e)}")


def get_config():
    """获取配置信息"""
    # 首先尝试加载.env文件
    load_env_file()
    
    # 获取配置
    config = {
        'api_key': os.getenv('DASHSCOPE_API_KEY'),
        'base_url': os.getenv('DASHSCOPE_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1'),
        'model_name': os.getenv('MODEL_NAME', 'qwen-vl-plus')
    }
    
    return config


def call_api_with_system_prompt(system_prompt, user_question, image_url, demo_name):
    """
    使用指定的系统提示词调用API
    
    Args:
        system_prompt: 系统提示词
        user_question: 用户问题
        image_url: 图像URL
        demo_name: 演示名称
    """
    print(f"\n🎯 {demo_name}")
    print("=" * 80)
    print(f"💬 系统提示词: {system_prompt}")
    print(f"❓ 用户问题: {user_question}")
    print(f"🖼️ 图像URL: {image_url}")
    print()
    
    # 获取配置
    config = get_config()
    
    if not config['api_key']:
        print("❌ 未找到API密钥！请检查.env文件配置")
        return
    
    # API配置
    url = f"{config['base_url']}/chat/completions"
    headers = {
        "Authorization": f"Bearer {config['api_key']}",
        "Content-Type": "application/json"
    }
    
    # 构建消息列表
    messages = []
    
    # 添加系统消息
    if system_prompt:
        messages.append({
            "role": "system",
            "content": system_prompt
        })
    
    # 添加用户消息
    messages.append({
        "role": "user",
        "content": [
            {
                "type": "image_url",
                "image_url": {"url": image_url}
            },
            {
                "type": "text",
                "text": user_question
            }
        ]
    })
    
    # 请求数据
    data = {
        "model": config['model_name'],
        "messages": messages
    }
    
    try:
        # 发送请求
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        if response.status_code == 200:
            response_data = response.json()
            answer = response_data["choices"][0]["message"]["content"]
            
            print("🤖 AI回答:")
            print("-" * 60)
            print(answer)
            print("-" * 60)
            
            # Token使用情况
            if "usage" in response_data:
                usage = response_data["usage"]
                print(f"📊 Token使用: 输入{usage.get('prompt_tokens', 0)} + 输出{usage.get('completion_tokens', 0)} = 总计{usage.get('total_tokens', 0)}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def main():
    """主函数 - 展示不同系统提示词的效果"""
    print("🚀 系统提示词效果对比演示")
    print("=" * 80)
    
    # 示例图像
    image_url = "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
    user_question = "请分析这张图片"
    
    # 不同的系统提示词演示
    demos = [
        {
            "name": "默认智能助手",
            "system_prompt": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。",
            "question": "请分析这张图片"
        },
        {
            "name": "专业摄影师视角",
            "system_prompt": "你是一位专业的摄影师和艺术评论家，具有丰富的摄影技术知识和艺术鉴赏能力。请从专业摄影的角度分析图片，包括构图、光线、色彩、情感表达等方面。",
            "question": "请从摄影专业角度分析这张图片"
        },
        {
            "name": "儿童教育专家",
            "system_prompt": "你是一位儿童教育专家，擅长用简单易懂、生动有趣的语言与孩子们交流。请用适合儿童理解的方式描述图片内容，并可以提出一些有趣的问题来启发孩子的思考。",
            "question": "请用适合小朋友的语言描述这张图片"
        },
        {
            "name": "心理学家视角",
            "system_prompt": "你是一位心理学专家，特别关注人类情感、行为和心理状态。请从心理学角度分析图片中的情感表达、人际关系和心理状态。",
            "question": "请从心理学角度分析图片中的情感和关系"
        },
        {
            "name": "诗人的浪漫视角",
            "system_prompt": "你是一位富有想象力的诗人，善于用优美的语言和诗意的表达来描述世界。请用诗意的语言来描述图片，可以运用比喻、拟人等修辞手法。",
            "question": "请用诗意的语言描述这张图片"
        }
    ]
    
    # 逐个演示不同的系统提示词效果
    for i, demo in enumerate(demos, 1):
        call_api_with_system_prompt(
            demo["system_prompt"],
            demo["question"],
            image_url,
            f"{i}. {demo['name']}"
        )
        
        # 在演示之间添加分隔
        if i < len(demos):
            input("\n按回车键继续下一个演示...")
    
    print("\n🎉 系统提示词演示完成！")
    print("通过对比可以看出，不同的系统提示词会显著影响AI的回答风格和内容重点。")


if __name__ == "__main__":
    main()
