#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问VL-Plus简单演示
快速测试图像理解功能
"""

import os
import json
import requests
from pathlib import Path


def load_env_file(env_path=".env"):
    """
    加载.env文件中的环境变量

    Args:
        env_path: .env文件路径，默认为当前目录下的.env
    """
    env_file = Path(env_path)
    if not env_file.exists():
        print(f"⚠️  未找到 {env_path} 文件，将使用系统环境变量")
        return

    print(f"📁 正在加载配置文件: {env_path}")

    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()

                # 跳过空行和注释行
                if not line or line.startswith('#'):
                    continue

                # 解析键值对
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()

                    # 移除引号
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]

                    # 设置环境变量（如果系统环境变量中没有的话）
                    if key not in os.environ:
                        os.environ[key] = value
                        print(f"  ✅ 加载: {key}")
                    else:
                        print(f"  ⚠️  跳过: {key} (系统环境变量已存在)")
                else:
                    print(f"  ❌ 第{line_num}行格式错误: {line}")

    except Exception as e:
        print(f"❌ 加载.env文件失败: {str(e)}")


def get_config():
    """
    获取配置信息

    Returns:
        dict: 包含API配置的字典
    """
    # 首先尝试加载.env文件
    load_env_file()

    # 获取配置
    config = {
        'api_key': os.getenv('DASHSCOPE_API_KEY'),
        'base_url': os.getenv('DASHSCOPE_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1'),
        'model_name': os.getenv('MODEL_NAME', 'qwen-vl-plus')
    }

    return config


def simple_image_demo(system_prompt=None, user_question=None, image_url=None):
    """
    简单的图像理解演示

    Args:
        system_prompt: 系统提示词，如果不提供则使用默认值
        user_question: 用户问题，如果不提供则使用默认值
        image_url: 图像URL，如果不提供则使用默认值
    """

    print("🚀 通义千问VL-Plus简单演示")
    print("=" * 60)

    # 获取配置
    config = get_config()

    # 检查API密钥
    if not config['api_key']:
        print("❌ 未找到API密钥！")
        print("请确保以下任一方式设置API密钥：")
        print("1. 创建.env文件并设置 DASHSCOPE_API_KEY=your-api-key")
        print("2. 设置系统环境变量: export DASHSCOPE_API_KEY='your-api-key'")
        print("3. 复制.env.example为.env并填入您的API密钥")
        return

    print(f"📋 使用配置:")
    print(f"  模型: {config['model_name']}")
    print(f"  API地址: {config['base_url']}")
    print(f"  API密钥: {config['api_key'][:10]}...{config['api_key'][-10:]}")
    print()

    # 默认配置
    default_system_prompt = "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。"
    default_user_question = "这张图片里有什么？请详细描述。"
    default_image_url = "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"

    # 使用提供的参数或默认值
    final_system_prompt = system_prompt or default_system_prompt
    final_user_question = user_question or default_user_question
    final_image_url = image_url or default_image_url

    print(f"💬 系统提示词: {final_system_prompt}")
    print(f"❓ 用户问题: {final_user_question}")
    print(f"🖼️ 图像URL: {final_image_url}")
    print()

    # API配置
    url = f"{config['base_url']}/chat/completions"
    headers = {
        "Authorization": f"Bearer {config['api_key']}",
        "Content-Type": "application/json"
    }

    # 构建消息列表
    messages = []

    # 添加系统消息
    if final_system_prompt:
        messages.append({
            "role": "system",
            "content": final_system_prompt
        })

    # 添加用户消息（包含图像和文本）
    messages.append({
        "role": "user",
        "content": [
            {
                "type": "image_url",
                "image_url": {
                    "url": final_image_url
                }
            },
            {
                "type": "text",
                "text": final_user_question
            }
        ]
    })

    # 请求数据
    data = {
        "model": config['model_name'],
        "messages": messages
    }
    
    print("🚀 发送请求到通义千问VL-Plus")
    print("=" * 60)
    
    # 打印请求信息
    print("📤 请求URL:", url)
    print("📤 请求头:")
    for key, value in headers.items():
        if key == "Authorization":
            print(f"  {key}: Bearer {value[:10]}...{value[-10:]}")
        else:
            print(f"  {key}: {value}")
    
    print("📤 请求体:")
    print(json.dumps(data, ensure_ascii=False, indent=2))
    print()
    
    try:
        # 发送请求
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        print("📥 响应状态码:", response.status_code)
        print("📥 响应头:")
        for key, value in response.headers.items():
            print(f"  {key}: {value}")
        
        print("📥 响应体:")
        response_data = response.json()
        print(json.dumps(response_data, ensure_ascii=False, indent=2))
        print()
        
        # 提取回答
        if response.status_code == 200 and "choices" in response_data:
            answer = response_data["choices"][0]["message"]["content"]
            print("🤖 AI回答:")
            print("-" * 40)
            print(answer)
            print("-" * 40)
            
            # Token使用情况
            if "usage" in response_data:
                usage = response_data["usage"]
                print("📊 Token使用情况:")
                print(f"  输入Token: {usage.get('input_tokens', 0)}")
                print(f"  输出Token: {usage.get('output_tokens', 0)}")
                print(f"  总Token: {usage.get('total_tokens', 0)}")
                if "image_tokens" in usage:
                    print(f"  图像Token: {usage['image_tokens']}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


def interactive_demo():
    """交互式演示，允许用户自定义参数"""
    print("🎯 交互式演示模式")
    print("=" * 60)

    # 询问用户是否要自定义参数
    use_custom = input("是否要自定义参数？(y/n，默认n): ").strip().lower()

    system_prompt = None
    user_question = None
    image_url = None

    if use_custom in ['y', 'yes', '是']:
        print("\n📝 请输入自定义参数（直接回车使用默认值）:")

        # 自定义系统提示词
        custom_system = input("\n💬 系统提示词（默认：智能助手提示词）: ").strip()
        if custom_system:
            system_prompt = custom_system

        # 自定义用户问题
        custom_question = input("\n❓ 用户问题（默认：这张图片里有什么？请详细描述。）: ").strip()
        if custom_question:
            user_question = custom_question

        # 自定义图像URL
        custom_image = input("\n🖼️ 图像URL（默认：官方示例图片）: ").strip()
        if custom_image:
            image_url = custom_image

    print("\n" + "🚀" * 20)
    print("开始API调用...")
    print("🚀" * 20)

    # 运行演示
    simple_image_demo(system_prompt, user_question, image_url)


def demo_with_custom_system_prompt():
    """使用您指定的系统提示词的演示"""
    system_prompt = "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。"

    print("🎯 使用自定义系统提示词的演示")
    print("=" * 60)
    print(f"💬 系统提示词: {system_prompt}")
    print()

    simple_image_demo(
        system_prompt=system_prompt,
        user_question="请分析这张图片，并从情感表达、构图美学、色彩搭配等角度给出专业的评价。",
        image_url="https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
    )


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "interactive":
            interactive_demo()
        elif sys.argv[1] == "custom":
            demo_with_custom_system_prompt()
        else:
            print("使用方法:")
            print("  python simple_demo.py           # 默认演示")
            print("  python simple_demo.py interactive  # 交互式演示")
            print("  python simple_demo.py custom      # 自定义系统提示词演示")
    else:
        # 默认演示
        simple_image_demo()
