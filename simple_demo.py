#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问VL-Plus简单演示
快速测试图像理解功能
"""

import os
import json
import requests


def simple_image_demo():
    """简单的图像理解演示"""
    
    # 检查API密钥
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("❌ 请设置DASHSCOPE_API_KEY环境变量")
        print("例如: export DASHSCOPE_API_KEY='your-api-key'")
        return
    
    # API配置
    url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # 请求数据
    data = {
        "model": "qwen-vl-plus",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
                        }
                    },
                    {
                        "type": "text",
                        "text": "这张图片里有什么？请详细描述。"
                    }
                ]
            }
        ]
    }
    
    print("🚀 发送请求到通义千问VL-Plus")
    print("=" * 60)
    
    # 打印请求信息
    print("📤 请求URL:", url)
    print("📤 请求头:")
    for key, value in headers.items():
        if key == "Authorization":
            print(f"  {key}: Bearer {value[:10]}...{value[-10:]}")
        else:
            print(f"  {key}: {value}")
    
    print("📤 请求体:")
    print(json.dumps(data, ensure_ascii=False, indent=2))
    print()
    
    try:
        # 发送请求
        response = requests.post(url, headers=headers, json=data, timeout=60)
        
        print("📥 响应状态码:", response.status_code)
        print("📥 响应头:")
        for key, value in response.headers.items():
            print(f"  {key}: {value}")
        
        print("📥 响应体:")
        response_data = response.json()
        print(json.dumps(response_data, ensure_ascii=False, indent=2))
        print()
        
        # 提取回答
        if response.status_code == 200 and "choices" in response_data:
            answer = response_data["choices"][0]["message"]["content"]
            print("🤖 AI回答:")
            print("-" * 40)
            print(answer)
            print("-" * 40)
            
            # Token使用情况
            if "usage" in response_data:
                usage = response_data["usage"]
                print("📊 Token使用情况:")
                print(f"  输入Token: {usage.get('input_tokens', 0)}")
                print(f"  输出Token: {usage.get('output_tokens', 0)}")
                print(f"  总Token: {usage.get('total_tokens', 0)}")
                if "image_tokens" in usage:
                    print(f"  图像Token: {usage['image_tokens']}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")


if __name__ == "__main__":
    simple_image_demo()
