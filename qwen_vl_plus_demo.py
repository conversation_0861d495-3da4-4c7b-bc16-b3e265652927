#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问VL-Plus多模态Demo
支持图像理解和视频理解，并打印详细的请求体和响应信息
"""

import os
import json
import requests
from typing import Dict, Any, List, Optional
from openai import OpenAI
from pathlib import Path


def load_env_file(env_path=".env"):
    """
    加载.env文件中的环境变量

    Args:
        env_path: .env文件路径，默认为当前目录下的.env
    """
    env_file = Path(env_path)
    if not env_file.exists():
        print(f"⚠️  未找到 {env_path} 文件，将使用系统环境变量")
        return

    print(f"📁 正在加载配置文件: {env_path}")

    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()

                # 跳过空行和注释行
                if not line or line.startswith('#'):
                    continue

                # 解析键值对
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()

                    # 移除引号
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]

                    # 设置环境变量（如果系统环境变量中没有的话）
                    if key not in os.environ:
                        os.environ[key] = value
                        print(f"  ✅ 加载: {key}")
                    else:
                        print(f"  ⚠️  跳过: {key} (系统环境变量已存在)")
                else:
                    print(f"  ❌ 第{line_num}行格式错误: {line}")

    except Exception as e:
        print(f"❌ 加载.env文件失败: {str(e)}")


class QwenVLPlusDemo:
    """通义千问VL-Plus多模态演示类"""

    def __init__(self, config: Optional[Dict] = None):
        """
        初始化Demo

        Args:
            config: 配置字典，如果不提供则自动加载.env文件和环境变量
        """
        if config is None:
            config = self.load_config()

        self.api_key = config.get('api_key')
        self.base_url = config.get('base_url', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
        self.model_name = config.get('model_name', 'qwen-vl-plus')

        if not self.api_key:
            raise ValueError("请设置DASHSCOPE_API_KEY环境变量或在.env文件中配置")

        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )

        print(f"📋 初始化配置:")
        print(f"  模型: {self.model_name}")
        print(f"  API地址: {self.base_url}")
        print(f"  API密钥: {self.api_key[:10]}...{self.api_key[-10:]}")
        print()

    @staticmethod
    def load_config():
        """
        加载配置信息

        Returns:
            dict: 包含API配置的字典
        """
        # 首先尝试加载.env文件
        load_env_file()

        # 获取配置
        config = {
            'api_key': os.getenv('DASHSCOPE_API_KEY'),
            'base_url': os.getenv('DASHSCOPE_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1'),
            'model_name': os.getenv('MODEL_NAME', 'qwen-vl-plus')
        }

        return config
    
    def print_request_info(self, method: str, url: str, headers: Dict, data: Dict):
        """打印请求信息"""
        print("=" * 80)
        print("📤 HTTP请求信息")
        print("=" * 80)
        print(f"方法: {method}")
        print(f"URL: {url}")
        print(f"请求头:")
        for key, value in headers.items():
            if key.lower() == 'authorization':
                print(f"  {key}: Bearer {value[:10]}...{value[-10:]}")
            else:
                print(f"  {key}: {value}")
        print(f"请求体:")
        print(json.dumps(data, ensure_ascii=False, indent=2))
        print()
    
    def print_response_info(self, response_data: Dict):
        """打印响应信息"""
        print("=" * 80)
        print("📥 HTTP响应信息")
        print("=" * 80)
        print(json.dumps(response_data, ensure_ascii=False, indent=2))
        print("=" * 80)
        print()
    
    def image_understanding_demo(self, image_url: str, question: str = "这是什么？"):
        """
        图像理解演示
        
        Args:
            image_url: 图像URL
            question: 要问的问题
        """
        print("🖼️ 图像理解演示")
        print(f"图像URL: {image_url}")
        print(f"问题: {question}")
        print()
        
        # 构建请求数据
        request_data = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {"url": image_url}
                        },
                        {
                            "type": "text",
                            "text": question
                        }
                    ]
                }
            ]
        }
        
        # 构建请求头
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 打印请求信息
        self.print_request_info(
            "POST", 
            f"{self.base_url}/chat/completions",
            headers,
            request_data
        )
        
        try:
            # 使用requests直接调用以便打印原始响应
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=request_data,
                timeout=60
            )
            
            response_data = response.json()
            self.print_response_info(response_data)
            
            # 提取并显示回答
            if response.status_code == 200 and "choices" in response_data:
                answer = response_data["choices"][0]["message"]["content"]
                print(f"🤖 AI回答: {answer}")
                
                # 显示Token使用情况
                if "usage" in response_data:
                    usage = response_data["usage"]
                    print(f"📊 Token使用情况:")
                    print(f"  输入Token: {usage.get('input_tokens', 0)}")
                    print(f"  输出Token: {usage.get('output_tokens', 0)}")
                    print(f"  总Token: {usage.get('total_tokens', 0)}")
                    if "image_tokens" in usage:
                        print(f"  图像Token: {usage['image_tokens']}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response_data}")
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
    
    def video_understanding_demo(self, video_frames: List[str], question: str = "描述这个视频的内容"):
        """
        视频理解演示（通过图像序列）
        
        Args:
            video_frames: 视频帧图像URL列表
            question: 要问的问题
        """
        print("🎬 视频理解演示")
        print(f"视频帧数量: {len(video_frames)}")
        print(f"问题: {question}")
        print()
        
        # 构建请求数据
        request_data = {
            "model": self.model_name,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "video",
                            "video": video_frames
                        },
                        {
                            "type": "text",
                            "text": question
                        }
                    ]
                }
            ]
        }
        
        # 构建请求头
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 打印请求信息
        self.print_request_info(
            "POST", 
            f"{self.base_url}/chat/completions",
            headers,
            request_data
        )
        
        try:
            # 使用requests直接调用以便打印原始响应
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=request_data,
                timeout=120
            )
            
            response_data = response.json()
            self.print_response_info(response_data)
            
            # 提取并显示回答
            if response.status_code == 200 and "choices" in response_data:
                answer = response_data["choices"][0]["message"]["content"]
                print(f"🤖 AI回答: {answer}")
                
                # 显示Token使用情况
                if "usage" in response_data:
                    usage = response_data["usage"]
                    print(f"📊 Token使用情况:")
                    print(f"  输入Token: {usage.get('input_tokens', 0)}")
                    print(f"  输出Token: {usage.get('output_tokens', 0)}")
                    print(f"  总Token: {usage.get('total_tokens', 0)}")
                    if "video_tokens" in usage:
                        print(f"  视频Token: {usage['video_tokens']}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response_data}")
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
    
    def stream_demo(self, image_url: str, question: str = "详细描述这张图片"):
        """
        流式输出演示
        
        Args:
            image_url: 图像URL
            question: 要问的问题
        """
        print("🌊 流式输出演示")
        print(f"图像URL: {image_url}")
        print(f"问题: {question}")
        print()
        
        try:
            completion = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {"url": image_url}
                            },
                            {
                                "type": "text",
                                "text": question
                            }
                        ]
                    }
                ],
                stream=True,
                stream_options={"include_usage": True}
            )
            
            print("🤖 AI流式回答:")
            full_response = ""
            for chunk in completion:
                chunk_dict = chunk.model_dump()
                print(f"📦 流式数据块: {json.dumps(chunk_dict, ensure_ascii=False)}")
                
                if chunk.choices and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    print(content, end="", flush=True)
                    full_response += content
                    
                # 打印使用情况（在最后一个chunk中）
                if hasattr(chunk, 'usage') and chunk.usage:
                    print(f"\n📊 Token使用情况: {chunk.usage}")
            
            print(f"\n\n✅ 完整回答: {full_response}")
            
        except Exception as e:
            print(f"❌ 流式请求异常: {str(e)}")


def main():
    """主函数"""
    print("🚀 通义千问VL-Plus多模态Demo启动")
    print("=" * 80)

    try:
        # 创建Demo实例（会自动加载.env配置）
        demo = QwenVLPlusDemo()
    except ValueError as e:
        print(f"❌ 配置错误: {str(e)}")
        print("请确保以下任一方式设置API密钥：")
        print("1. 创建.env文件并设置 DASHSCOPE_API_KEY=your-api-key")
        print("2. 设置系统环境变量: export DASHSCOPE_API_KEY='your-api-key'")
        print("3. 复制.env.example为.env并填入您的API密钥")
        return
    
    # 示例图像URL（来自文档）
    image_url = "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
    
    # 示例视频帧URL（来自文档）
    video_frames = [
        "https://img.alicdn.com/imgextra/i3/O1CN01K3SgGo1eqmlUgeE9b_!!6000000003923-0-tps-3840-2160.jpg",
        "https://img.alicdn.com/imgextra/i4/O1CN01BjZvwg1Y23CF5qIRB_!!6000000003000-0-tps-3840-2160.jpg",
        "https://img.alicdn.com/imgextra/i4/O1CN01Ib0clU27vTgBdbVLQ_!!6000000007859-0-tps-3840-2160.jpg",
        "https://img.alicdn.com/imgextra/i1/O1CN01aygPLW1s3EXCdSN4X_!!6000000005710-0-tps-3840-2160.jpg"
    ]
    
    try:
        # 1. 图像理解演示
        print("\n" + "🔥" * 40)
        print("1️⃣ 图像理解演示")
        print("🔥" * 40)
        demo.image_understanding_demo(image_url, "这张图片里有什么？请详细描述。")
        
        input("\n按回车键继续下一个演示...")
        
        # 2. 视频理解演示
        print("\n" + "🔥" * 40)
        print("2️⃣ 视频理解演示")
        print("🔥" * 40)
        demo.video_understanding_demo(video_frames, "描述这个视频序列的具体过程")
        
        input("\n按回车键继续下一个演示...")
        
        # 3. 流式输出演示
        print("\n" + "🔥" * 40)
        print("3️⃣ 流式输出演示")
        print("🔥" * 40)
        demo.stream_demo(image_url, "请详细分析这张图片的构图、色彩和情感表达")
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
    
    print("\n🎉 Demo演示完成！")


if __name__ == "__main__":
    main()
