#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问VL-Plus多模态Demo
支持图像理解和视频理解，并打印详细的请求体和响应信息
"""

import os
import json
import requests
from typing import Dict, Any, List, Optional
from openai import OpenAI


class QwenVLPlusDemo:
    """通义千问VL-Plus多模态演示类"""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        初始化Demo
        
        Args:
            api_key: API密钥，如果不提供则从环境变量DASHSCOPE_API_KEY获取
        """
        self.api_key = api_key or os.getenv("DASHSCOPE_API_KEY")
        if not self.api_key:
            raise ValueError("请设置DASHSCOPE_API_KEY环境变量或提供api_key参数")
        
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
    
    def print_request_info(self, method: str, url: str, headers: Dict, data: Dict):
        """打印请求信息"""
        print("=" * 80)
        print("📤 HTTP请求信息")
        print("=" * 80)
        print(f"方法: {method}")
        print(f"URL: {url}")
        print(f"请求头:")
        for key, value in headers.items():
            if key.lower() == 'authorization':
                print(f"  {key}: Bearer {value[:10]}...{value[-10:]}")
            else:
                print(f"  {key}: {value}")
        print(f"请求体:")
        print(json.dumps(data, ensure_ascii=False, indent=2))
        print()
    
    def print_response_info(self, response_data: Dict):
        """打印响应信息"""
        print("=" * 80)
        print("📥 HTTP响应信息")
        print("=" * 80)
        print(json.dumps(response_data, ensure_ascii=False, indent=2))
        print("=" * 80)
        print()
    
    def image_understanding_demo(self, image_url: str, question: str = "这是什么？"):
        """
        图像理解演示
        
        Args:
            image_url: 图像URL
            question: 要问的问题
        """
        print("🖼️ 图像理解演示")
        print(f"图像URL: {image_url}")
        print(f"问题: {question}")
        print()
        
        # 构建请求数据
        request_data = {
            "model": "qwen-vl-plus",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {"url": image_url}
                        },
                        {
                            "type": "text",
                            "text": question
                        }
                    ]
                }
            ]
        }
        
        # 构建请求头
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 打印请求信息
        self.print_request_info(
            "POST", 
            f"{self.base_url}/chat/completions",
            headers,
            request_data
        )
        
        try:
            # 使用requests直接调用以便打印原始响应
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=request_data,
                timeout=60
            )
            
            response_data = response.json()
            self.print_response_info(response_data)
            
            # 提取并显示回答
            if response.status_code == 200 and "choices" in response_data:
                answer = response_data["choices"][0]["message"]["content"]
                print(f"🤖 AI回答: {answer}")
                
                # 显示Token使用情况
                if "usage" in response_data:
                    usage = response_data["usage"]
                    print(f"📊 Token使用情况:")
                    print(f"  输入Token: {usage.get('input_tokens', 0)}")
                    print(f"  输出Token: {usage.get('output_tokens', 0)}")
                    print(f"  总Token: {usage.get('total_tokens', 0)}")
                    if "image_tokens" in usage:
                        print(f"  图像Token: {usage['image_tokens']}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response_data}")
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
    
    def video_understanding_demo(self, video_frames: List[str], question: str = "描述这个视频的内容"):
        """
        视频理解演示（通过图像序列）
        
        Args:
            video_frames: 视频帧图像URL列表
            question: 要问的问题
        """
        print("🎬 视频理解演示")
        print(f"视频帧数量: {len(video_frames)}")
        print(f"问题: {question}")
        print()
        
        # 构建请求数据
        request_data = {
            "model": "qwen-vl-plus",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "video",
                            "video": video_frames
                        },
                        {
                            "type": "text",
                            "text": question
                        }
                    ]
                }
            ]
        }
        
        # 构建请求头
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        # 打印请求信息
        self.print_request_info(
            "POST", 
            f"{self.base_url}/chat/completions",
            headers,
            request_data
        )
        
        try:
            # 使用requests直接调用以便打印原始响应
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=request_data,
                timeout=120
            )
            
            response_data = response.json()
            self.print_response_info(response_data)
            
            # 提取并显示回答
            if response.status_code == 200 and "choices" in response_data:
                answer = response_data["choices"][0]["message"]["content"]
                print(f"🤖 AI回答: {answer}")
                
                # 显示Token使用情况
                if "usage" in response_data:
                    usage = response_data["usage"]
                    print(f"📊 Token使用情况:")
                    print(f"  输入Token: {usage.get('input_tokens', 0)}")
                    print(f"  输出Token: {usage.get('output_tokens', 0)}")
                    print(f"  总Token: {usage.get('total_tokens', 0)}")
                    if "video_tokens" in usage:
                        print(f"  视频Token: {usage['video_tokens']}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response_data}")
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
    
    def stream_demo(self, image_url: str, question: str = "详细描述这张图片"):
        """
        流式输出演示
        
        Args:
            image_url: 图像URL
            question: 要问的问题
        """
        print("🌊 流式输出演示")
        print(f"图像URL: {image_url}")
        print(f"问题: {question}")
        print()
        
        try:
            completion = self.client.chat.completions.create(
                model="qwen-vl-plus",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {"url": image_url}
                            },
                            {
                                "type": "text",
                                "text": question
                            }
                        ]
                    }
                ],
                stream=True,
                stream_options={"include_usage": True}
            )
            
            print("🤖 AI流式回答:")
            full_response = ""
            for chunk in completion:
                chunk_dict = chunk.model_dump()
                print(f"📦 流式数据块: {json.dumps(chunk_dict, ensure_ascii=False)}")
                
                if chunk.choices and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    print(content, end="", flush=True)
                    full_response += content
                    
                # 打印使用情况（在最后一个chunk中）
                if hasattr(chunk, 'usage') and chunk.usage:
                    print(f"\n📊 Token使用情况: {chunk.usage}")
            
            print(f"\n\n✅ 完整回答: {full_response}")
            
        except Exception as e:
            print(f"❌ 流式请求异常: {str(e)}")


def main():
    """主函数"""
    print("🚀 通义千问VL-Plus多模态Demo启动")
    print("=" * 80)
    
    # 检查API密钥
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("❌ 请设置DASHSCOPE_API_KEY环境变量")
        print("例如: export DASHSCOPE_API_KEY='your-api-key'")
        return
    
    # 创建Demo实例
    demo = QwenVLPlusDemo()
    
    # 示例图像URL（来自文档）
    image_url = "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
    
    # 示例视频帧URL（来自文档）
    video_frames = [
        "https://img.alicdn.com/imgextra/i3/O1CN01K3SgGo1eqmlUgeE9b_!!6000000003923-0-tps-3840-2160.jpg",
        "https://img.alicdn.com/imgextra/i4/O1CN01BjZvwg1Y23CF5qIRB_!!6000000003000-0-tps-3840-2160.jpg",
        "https://img.alicdn.com/imgextra/i4/O1CN01Ib0clU27vTgBdbVLQ_!!6000000007859-0-tps-3840-2160.jpg",
        "https://img.alicdn.com/imgextra/i1/O1CN01aygPLW1s3EXCdSN4X_!!6000000005710-0-tps-3840-2160.jpg"
    ]
    
    try:
        # 1. 图像理解演示
        print("\n" + "🔥" * 40)
        print("1️⃣ 图像理解演示")
        print("🔥" * 40)
        demo.image_understanding_demo(image_url, "这张图片里有什么？请详细描述。")
        
        input("\n按回车键继续下一个演示...")
        
        # 2. 视频理解演示
        print("\n" + "🔥" * 40)
        print("2️⃣ 视频理解演示")
        print("🔥" * 40)
        demo.video_understanding_demo(video_frames, "描述这个视频序列的具体过程")
        
        input("\n按回车键继续下一个演示...")
        
        # 3. 流式输出演示
        print("\n" + "🔥" * 40)
        print("3️⃣ 流式输出演示")
        print("🔥" * 40)
        demo.stream_demo(image_url, "请详细分析这张图片的构图、色彩和情感表达")
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")
    
    print("\n🎉 Demo演示完成！")


if __name__ == "__main__":
    main()
