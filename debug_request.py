#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试请求体问题的测试脚本
用于逐步测试不同字段组合，找出导致请求失败的原因
"""

import os
import json
import requests
from pathlib import Path
import copy


def load_env_file(env_path=".env"):
    """加载.env文件中的环境变量"""
    env_file = Path(env_path)
    if not env_file.exists():
        print(f"⚠️  未找到 {env_path} 文件，将使用系统环境变量")
        return
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    if key not in os.environ:
                        os.environ[key] = value
    except Exception as e:
        print(f"❌ 加载.env文件失败: {str(e)}")


def get_config():
    """获取配置信息"""
    load_env_file()
    return {
        'api_key': os.getenv('DASHSCOPE_API_KEY'),
        'base_url': os.getenv('DASHSCOPE_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1'),
        'model_name': os.getenv('MODEL_NAME', 'qwen-vl-plus')
    }


def test_request(request_data, test_name):
    """
    测试单个请求
    
    Args:
        request_data: 请求数据
        test_name: 测试名称
    """
    print(f"\n🧪 测试: {test_name}")
    print("=" * 80)
    
    config = get_config()
    if not config['api_key']:
        print("❌ 未找到API密钥！")
        return False
    
    url = f"{config['base_url']}/chat/completions"
    headers = {
        "Authorization": f"Bearer {config['api_key']}",
        "Content-Type": "application/json"
    }
    
    print("📤 请求体:")
    print(json.dumps(request_data, ensure_ascii=False, indent=2))
    print()
    
    try:
        response = requests.post(url, headers=headers, json=request_data, timeout=30)
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 请求成功")
            response_data = response.json()
            if "choices" in response_data:
                print(f"🤖 回答: {response_data['choices'][0]['message']['content'][:100]}...")
            return True
        else:
            print("❌ 请求失败")
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False


def main():
    """主函数 - 逐步测试不同的请求体配置"""
    print("🔍 请求体调试工具")
    print("=" * 80)
    
    # 基础成功的请求体
    base_successful_request = {
        "max_tokens": 8192,
        "messages": [
            {
                "content": [
                    {
                        "text": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。",
                        "type": "text"
                    }
                ],
                "role": "system"
            },
            {
                "content": [
                    {
                        "text": "你好",
                        "type": "text"
                    }
                ],
                "role": "user"
            }
        ],
        "model": "qwen-vl-ocr-latest",
        "stream": True,
        "stream_options": {
            "include_usage": True
        },
        "tool_choice": "none",
        "tools": [
            {
                "function": {
                    "name": "NonInvocableTool"
                },
                "type": "function"
            }
        ]
    }
    
    # 问题请求体（包含换行符）
    problematic_request = {
        "max_tokens": 8192,
        "messages": [
            {
                "content": [
                    {
                        "text": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各 \r\n种任务。请根据用户的需求，提供准确、有用和友好的回答。",
                        "type": "text"
                    }
                ],
                "role": "system"
            },
            {
                "content": [
                    {
                        "text": "你好",
                        "type": "text"
                    }
                ],
                "role": "user"
            }
        ],
        "model": "qwen-vl-ocr-latest",
        "stream": True,
        "stream_options": {
            "include_usage": True
        },
        "tool_choice": "none",
        "tools": [
            {
                "function": {
                    "name": "NonInvocableTool"
                },
                "type": "function"
            }
        ]
    }
    
    # 测试序列
    tests = [
        # 1. 测试基础成功请求
        (copy.deepcopy(base_successful_request), "基础成功请求"),
        
        # 2. 测试问题请求（包含\\r\\n）
        (copy.deepcopy(problematic_request), "问题请求（包含\\r\\n）"),
        
        # 3. 测试清理换行符后的请求
        (copy.deepcopy(problematic_request), "清理换行符后的请求"),
        
        # 4. 测试移除tools字段
        (copy.deepcopy(problematic_request), "移除tools字段"),
        
        # 5. 测试移除tool_choice字段
        (copy.deepcopy(problematic_request), "移除tool_choice字段"),
        
        # 6. 测试移除stream相关字段
        (copy.deepcopy(problematic_request), "移除stream相关字段"),
        
        # 7. 测试简化的消息格式
        (copy.deepcopy(problematic_request), "简化消息格式"),
    ]
    
    # 修改测试数据
    # 3. 清理换行符
    tests[2][0]["messages"][0]["content"][0]["text"] = tests[2][0]["messages"][0]["content"][0]["text"].replace("\r\n", "")
    
    # 4. 移除tools字段
    if "tools" in tests[3][0]:
        del tests[3][0]["tools"]
    if "tool_choice" in tests[3][0]:
        del tests[3][0]["tool_choice"]
    
    # 5. 移除tool_choice字段（保留tools）
    if "tool_choice" in tests[4][0]:
        del tests[4][0]["tool_choice"]
    
    # 6. 移除stream相关字段
    tests[5][0]["stream"] = False
    if "stream_options" in tests[5][0]:
        del tests[5][0]["stream_options"]
    if "tools" in tests[5][0]:
        del tests[5][0]["tools"]
    if "tool_choice" in tests[5][0]:
        del tests[5][0]["tool_choice"]
    
    # 7. 简化消息格式
    tests[6][0]["messages"] = [
        {
            "role": "system",
            "content": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。"
        },
        {
            "role": "user",
            "content": "你好"
        }
    ]
    if "tools" in tests[6][0]:
        del tests[6][0]["tools"]
    if "tool_choice" in tests[6][0]:
        del tests[6][0]["tool_choice"]
    
    # 执行测试
    results = []
    for i, (request_data, test_name) in enumerate(tests, 1):
        print(f"\n{'🔥' * 20} 测试 {i}/{len(tests)} {'🔥' * 20}")
        success = test_request(request_data, test_name)
        results.append((test_name, success))
        
        if i < len(tests):
            input("\n按回车键继续下一个测试...")
    
    # 总结结果
    print(f"\n{'🎯' * 20} 测试总结 {'🎯' * 20}")
    print("=" * 80)
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{status} - {test_name}")
    
    print("\n🔍 分析建议:")
    print("1. 如果清理换行符后成功，说明问题是\\r\\n字符")
    print("2. 如果移除tools后成功，说明tools配置有问题")
    print("3. 如果简化消息格式后成功，说明content数组格式有问题")
    print("4. 如果移除stream后成功，说明流式输出配置有问题")


if __name__ == "__main__":
    main()
