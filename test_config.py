#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置测试脚本
用于验证.env文件是否正确加载
"""

import os
from pathlib import Path


def load_env_file(env_path=".env"):
    """加载.env文件中的环境变量"""
    env_file = Path(env_path)
    if not env_file.exists():
        print(f"❌ 未找到 {env_path} 文件")
        return False
    
    print(f"📁 正在加载配置文件: {env_path}")
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                
                # 跳过空行和注释行
                if not line or line.startswith('#'):
                    continue
                
                # 解析键值对
                if '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # 移除引号
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                    
                    # 设置环境变量（如果系统环境变量中没有的话）
                    if key not in os.environ:
                        os.environ[key] = value
                        print(f"  ✅ 加载: {key} = {value[:20]}{'...' if len(value) > 20 else ''}")
                    else:
                        print(f"  ⚠️  跳过: {key} (系统环境变量已存在)")
                else:
                    print(f"  ❌ 第{line_num}行格式错误: {line}")
        return True
    
    except Exception as e:
        print(f"❌ 加载.env文件失败: {str(e)}")
        return False


def test_config():
    """测试配置加载"""
    print("🧪 配置测试开始")
    print("=" * 50)
    
    # 加载.env文件
    if not load_env_file():
        return
    
    print("\n📋 当前配置:")
    print("-" * 30)
    
    # 检查必需的配置
    required_configs = {
        'DASHSCOPE_API_KEY': '阿里云API密钥',
        'DASHSCOPE_BASE_URL': 'API基础URL',
        'MODEL_NAME': '模型名称'
    }
    
    all_ok = True
    
    for key, description in required_configs.items():
        value = os.getenv(key)
        if value:
            if key == 'DASHSCOPE_API_KEY':
                # 隐藏API密钥的大部分内容
                display_value = f"{value[:10]}...{value[-10:]}" if len(value) > 20 else value
            else:
                display_value = value
            print(f"✅ {description}: {display_value}")
        else:
            print(f"❌ {description}: 未设置")
            all_ok = False
    
    print("\n🔍 配置验证:")
    print("-" * 30)
    
    if all_ok:
        print("✅ 所有必需配置都已正确设置")
        
        # 验证API密钥格式
        api_key = os.getenv('DASHSCOPE_API_KEY')
        if api_key and api_key != 'your-api-key-here':
            if api_key.startswith('sk-'):
                print("✅ API密钥格式看起来正确")
            else:
                print("⚠️  API密钥格式可能不正确（应该以'sk-'开头）")
        else:
            print("❌ 请将API密钥替换为实际的密钥")
            all_ok = False
        
        # 验证URL格式
        base_url = os.getenv('DASHSCOPE_BASE_URL')
        if base_url and base_url.startswith('https://'):
            print("✅ API基础URL格式正确")
        else:
            print("⚠️  API基础URL格式可能不正确")
        
        # 验证模型名称
        model_name = os.getenv('MODEL_NAME')
        if model_name and 'qwen' in model_name.lower():
            print("✅ 模型名称看起来正确")
        else:
            print("⚠️  模型名称可能不正确")
    
    print("\n" + "=" * 50)
    if all_ok:
        print("🎉 配置测试通过！可以运行演示程序了")
        print("运行命令:")
        print("  python qwen_vl_plus_demo.py")
        print("  python simple_demo.py")
    else:
        print("❌ 配置测试失败，请检查.env文件")
        print("确保:")
        print("1. .env文件存在于当前目录")
        print("2. DASHSCOPE_API_KEY设置为实际的API密钥")
        print("3. 其他配置项格式正确")


if __name__ == "__main__":
    test_config()
