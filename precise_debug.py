#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确调试：对比失败和成功的请求体，找出真正的差异
"""

import os
import json
import requests
from pathlib import Path


def load_env_file():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        if key not in os.environ:
                            os.environ[key] = value
        except Exception as e:
            print(f"❌ 加载.env文件失败: {str(e)}")


def test_request(request_data, test_name):
    """测试请求"""
    print(f"\n🧪 {test_name}")
    print("=" * 80)
    
    load_env_file()
    api_key = os.getenv('DASHSCOPE_API_KEY')
    base_url = os.getenv('DASHSCOPE_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
    
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("📤 请求体:")
    print(json.dumps(request_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, headers=headers, json=request_data, timeout=30)
        print(f"\n📥 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 成功")
            return True
        else:
            print("❌ 失败")
            print(f"错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return False


def main():
    """主函数"""
    print("🔍 精确调试：对比失败和成功的请求体")
    print("=" * 80)
    
    # 您说的成功请求体（去掉了换行符差异）
    successful_request = {
        "max_tokens": 8192,
        "messages": [
            {
                "content": [
                    {
                        "text": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。",
                        "type": "text"
                    }
                ],
                "role": "system"
            },
            {
                "content": [
                    {
                        "text": "你好",
                        "type": "text"
                    }
                ],
                "role": "user"
            }
        ],
        "model": "qwen-vl-ocr-latest",
        "stream": True,
        "stream_options": {
            "include_usage": True
        },
        "tool_choice": "none",
        "tools": [
            {
                "function": {
                    "name": "NonInvocableTool"
                },
                "type": "function"
            }
        ]
    }
    
    # 您说的失败请求体（包含\r\n）
    failing_request = {
        "max_tokens": 8192,
        "messages": [
            {
                "content": [
                    {
                        "text": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各 \r\n种任务。请根据用户的需求，提供准确、有用和友好的回答。",
                        "type": "text"
                    }
                ],
                "role": "system"
            },
            {
                "content": [
                    {
                        "text": "你好",
                        "type": "text"
                    }
                ],
                "role": "user"
            }
        ],
        "model": "qwen-vl-ocr-latest",
        "stream": True,
        "stream_options": {
            "include_usage": True
        },
        "tool_choice": "none",
        "tools": [
            {
                "function": {
                    "name": "NonInvocableTool"
                },
                "type": "function"
            }
        ]
    }
    
    # 测试各种变体
    tests = [
        (successful_request, "您说的成功请求体"),
        (failing_request, "您说的失败请求体（包含\\r\\n）"),
    ]
    
    # 创建更多测试变体
    # 测试3: 清理换行符
    clean_request = json.loads(json.dumps(failing_request))
    clean_request["messages"][0]["content"][0]["text"] = clean_request["messages"][0]["content"][0]["text"].replace("\r\n", "")
    tests.append((clean_request, "清理\\r\\n后的请求体"))
    
    # 测试4: 移除tools中的description和parameters
    fixed_tools_request = json.loads(json.dumps(clean_request))
    fixed_tools_request["tools"][0]["function"]["description"] = "A non-invocable tool"
    fixed_tools_request["tools"][0]["function"]["parameters"] = {}
    tests.append((fixed_tools_request, "修复tools配置"))
    
    # 测试5: 简化tools配置
    simple_tools_request = json.loads(json.dumps(clean_request))
    simple_tools_request["tools"] = [
        {
            "type": "function",
            "function": {
                "name": "NonInvocableTool",
                "description": "A tool that cannot be invoked",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        }
    ]
    tests.append((simple_tools_request, "完整tools配置"))
    
    # 测试6: 移除tools
    no_tools_request = json.loads(json.dumps(clean_request))
    del no_tools_request["tools"]
    del no_tools_request["tool_choice"]
    tests.append((no_tools_request, "移除tools配置"))
    
    # 执行测试
    results = []
    for test_data, test_name in tests:
        success = test_request(test_data, test_name)
        results.append((test_name, success))
        print("\n" + "-" * 80)
    
    # 总结
    print(f"\n🎯 测试总结:")
    print("=" * 80)
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    # 分析差异
    print(f"\n🔍 差异分析:")
    print("如果某些测试成功，某些失败，问题可能在于：")
    print("1. \\r\\n 换行符导致的文本解析问题")
    print("2. tools 配置格式不完整")
    print("3. function 定义缺少必要字段")
    print("4. 其他细微的格式差异")


if __name__ == "__main__":
    main()
