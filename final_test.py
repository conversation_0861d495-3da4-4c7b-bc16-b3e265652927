#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试：找出content数组格式的真正问题
"""

import os
import json
import requests
from pathlib import Path


def load_env_file():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        if key not in os.environ:
                            os.environ[key] = value
        except Exception as e:
            print(f"❌ 加载.env文件失败: {str(e)}")


def test_request(request_data, test_name):
    """测试请求"""
    print(f"\n🧪 {test_name}")
    print("=" * 60)
    
    load_env_file()
    api_key = os.getenv('DASHSCOPE_API_KEY')
    base_url = os.getenv('DASHSCOPE_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
    
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("📤 请求体:")
    print(json.dumps(request_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, headers=headers, json=request_data, timeout=30)
        print(f"\n📥 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 成功")
            return True
        else:
            print("❌ 失败")
            error_text = response.text
            if len(error_text) > 300:
                error_text = error_text[:300] + "..."
            print(f"错误: {error_text}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return False


def main():
    """主函数"""
    print("🔍 最终测试：content数组格式问题诊断")
    print("=" * 60)
    
    print("💡 关键发现：所有content数组格式都失败了！")
    print("这说明问题在于qwen-vl-ocr-latest模型不支持这种消息格式")
    print()
    
    # 测试1: 标准字符串格式（应该成功）
    test1 = {
        "model": "qwen-vl-ocr-latest",
        "messages": [
            {
                "role": "system",
                "content": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。"
            },
            {
                "role": "user",
                "content": "你好"
            }
        ],
        "max_tokens": 100
    }
    
    # 测试2: 数组格式但只有文本（应该失败）
    test2 = {
        "model": "qwen-vl-ocr-latest",
        "messages": [
            {
                "role": "system",
                "content": [
                    {
                        "type": "text",
                        "text": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。"
                    }
                ]
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "你好"
                    }
                ]
            }
        ],
        "max_tokens": 100
    }
    
    # 测试3: 数组格式包含图像（应该成功）
    test3 = {
        "model": "qwen-vl-ocr-latest",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
                        }
                    },
                    {
                        "type": "text",
                        "text": "你好，请分析这张图片"
                    }
                ]
            }
        ],
        "max_tokens": 100
    }
    
    # 测试4: 混合格式（system用字符串，user用数组+图像）
    test4 = {
        "model": "qwen-vl-ocr-latest",
        "messages": [
            {
                "role": "system",
                "content": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。"
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
                        }
                    },
                    {
                        "type": "text",
                        "text": "你好，请分析这张图片"
                    }
                ]
            }
        ],
        "max_tokens": 100
    }
    
    # 测试5: 使用qwen-plus模型测试数组格式
    test5 = {
        "model": "qwen-plus",
        "messages": [
            {
                "role": "system",
                "content": [
                    {
                        "type": "text",
                        "text": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。"
                    }
                ]
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "你好"
                    }
                ]
            }
        ],
        "max_tokens": 100
    }
    
    tests = [
        (test1, "qwen-vl-ocr-latest + 字符串格式"),
        (test2, "qwen-vl-ocr-latest + 数组格式（纯文本）"),
        (test3, "qwen-vl-ocr-latest + 数组格式（含图像）"),
        (test4, "qwen-vl-ocr-latest + 混合格式"),
        (test5, "qwen-plus + 数组格式（纯文本）")
    ]
    
    results = []
    for test_data, test_name in tests:
        success = test_request(test_data, test_name)
        results.append((test_name, success))
        print("\n" + "-" * 60)
    
    # 总结
    print(f"\n🎯 最终结论:")
    print("=" * 60)
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n💡 问题根源分析:")
    print("基于测试结果，问题很可能是：")
    print("1. qwen-vl-ocr-latest 模型不支持 content 数组格式的纯文本消息")
    print("2. content 数组格式只在包含图像等多媒体内容时才被支持")
    print("3. 对于纯文本，必须使用字符串格式的 content")
    print("4. 不同模型对消息格式的支持程度不同")
    
    print(f"\n🔧 修复建议:")
    print("将您的请求体修改为：")
    print("❌ 错误: content: [{'type': 'text', 'text': '...'}]")
    print("✅ 正确: content: '...'")


if __name__ == "__main__":
    main()
