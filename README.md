# 通义千问VL-Plus多模态Demo

这是一个基于阿里云通义千问VL-Plus模型的多模态演示程序，支持图像理解和视频理解功能，并会详细打印HTTP请求体和响应信息。

## 功能特性

- 🖼️ **图像理解**: 支持分析单张图片内容
- 🎬 **视频理解**: 支持通过图像序列分析视频内容
- 🌊 **流式输出**: 支持实时流式响应
- 📊 **详细日志**: 打印完整的HTTP请求体和响应信息
- 💰 **Token统计**: 显示详细的Token使用情况

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置API密钥

程序支持多种配置方式，按优先级排序：

### 方式1：使用.env文件（推荐）

1. 编辑项目根目录下的 `.env` 文件
2. 将 `DASHSCOPE_API_KEY=your-api-key-here` 中的 `your-api-key-here` 替换为您的实际API密钥

```bash
# .env文件内容示例
DASHSCOPE_API_KEY=sk-your-actual-api-key-here
DASHSCOPE_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
MODEL_NAME=qwen-vl-plus
```

### 方式2：系统环境变量

```bash
export DASHSCOPE_API_KEY="your-api-key-here"
```

或者在Windows系统中：

```cmd
set DASHSCOPE_API_KEY=your-api-key-here
```

> 注意：系统环境变量的优先级高于.env文件

## 获取API密钥

1. 访问 [阿里云百炼控制台](https://bailian.console.aliyun.com/)
2. 登录您的阿里云账号
3. 在API管理页面获取您的API Key

## 运行演示

程序启动时会自动加载 `.env` 文件中的配置：

1. **运行完整演示**：
   ```bash
   python qwen_vl_plus_demo.py
   ```

2. **运行简单演示**：
   ```bash
   python simple_demo.py
   ```

启动时会显示配置加载信息：
```
📁 正在加载配置文件: .env
  ✅ 加载: DASHSCOPE_API_KEY
  ✅ 加载: DASHSCOPE_BASE_URL
  ✅ 加载: MODEL_NAME
📋 初始化配置:
  模型: qwen-vl-plus
  API地址: https://dashscope.aliyuncs.com/compatible-mode/v1
  API密钥: sk-c6b73c9...ec3c02c
```

## 演示内容

程序包含三个主要演示：

### 1. 图像理解演示
- 分析单张图片内容
- 显示完整的HTTP请求和响应
- 统计Token使用情况

### 2. 视频理解演示
- 通过图像序列分析视频内容
- 支持多帧图像输入
- 理解视频的时序变化

### 3. 流式输出演示
- 实时流式响应
- 逐块显示生成内容
- 展示流式数据格式

## 输出示例

程序会打印详细的请求和响应信息：

```
================================================================================
📤 HTTP请求信息
================================================================================
方法: POST
URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
请求头:
  Authorization: Bearer sk-xxxxxxxx...xxxxxxxx
  Content-Type: application/json
请求体:
{
  "model": "qwen-vl-plus",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "image_url",
          "image_url": {
            "url": "https://example.com/image.jpg"
          }
        },
        {
          "type": "text",
          "text": "这是什么？"
        }
      ]
    }
  ]
}

================================================================================
📥 HTTP响应信息
================================================================================
{
  "choices": [
    {
      "finish_reason": "stop",
      "message": {
        "role": "assistant",
        "content": "这是一张包含小狗和女孩的温馨照片..."
      }
    }
  ],
  "usage": {
    "input_tokens": 1234,
    "output_tokens": 567,
    "total_tokens": 1801,
    "image_tokens": 1000
  }
}
```

## 自定义使用

您可以修改代码中的图像URL和问题来测试不同的场景：

```python
# 自定义图像URL
image_url = "https://your-image-url.jpg"

# 自定义问题
question = "请详细描述这张图片的内容"

# 调用演示
demo.image_understanding_demo(image_url, question)
```

## 支持的模型

- qwen-vl-plus
- qwen-vl-max
- qwen-vl-max-latest
- 其他通义千问VL系列模型

## 注意事项

1. 确保您的API密钥有足够的额度
2. 图像URL必须是公开可访问的
3. 视频理解功能通过图像序列实现
4. 流式输出可能会产生更多的网络请求

## 错误处理

程序包含完整的错误处理机制：
- API密钥验证
- 网络请求异常处理
- 响应格式验证
- 用户中断处理

## 快速开始

1. **克隆或下载项目文件**
2. **安装依赖**：`pip install -r requirements.txt`
3. **配置API密钥**：编辑 `.env` 文件，设置您的API密钥
4. **测试配置**：`python test_config.py`
5. **运行演示**：`python simple_demo.py`

## 故障排除

### 常见问题

**Q: 提示"未找到API密钥"**
A: 请确保：
- `.env` 文件存在于项目根目录
- `DASHSCOPE_API_KEY` 已设置为实际的API密钥（不是 `your-api-key-here`）
- API密钥格式正确（以 `sk-` 开头）

**Q: 请求失败，返回401错误**
A: API密钥无效或已过期，请检查：
- API密钥是否正确
- 账户是否有足够的额度
- API密钥是否有相应的权限

**Q: 请求超时**
A: 网络连接问题，请检查：
- 网络连接是否正常
- 是否需要代理设置
- 防火墙是否阻止了请求

**Q: 模型不支持某些功能**
A: 不同模型支持的功能不同，请参考[模型列表](https://help.aliyun.com/zh/model-studio/models)

### 配置测试

运行配置测试脚本来验证设置：
```bash
python test_config.py
```

## 技术细节

- 使用OpenAI兼容的API接口
- 支持多种内容类型（文本、图像、视频）
- 实现了详细的请求/响应日志
- 提供了Token使用统计
- 自动加载.env配置文件
- 完整的错误处理和用户友好的提示

## 文件说明

- `qwen_vl_plus_demo.py` - 完整功能演示（图像、视频、流式输出）
- `simple_demo.py` - 简化版演示（快速测试）
- `test_config.py` - 配置测试脚本
- `.env` - 配置文件（包含API密钥等）
- `.env.example` - 配置文件模板
- `requirements.txt` - Python依赖列表

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个演示程序。
