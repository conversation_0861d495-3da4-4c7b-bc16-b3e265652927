# 通义千问VL-Plus多模态Demo

这是一个基于阿里云通义千问VL-Plus模型的多模态演示程序，支持图像理解和视频理解功能，并会详细打印HTTP请求体和响应信息。

## 功能特性

- 🖼️ **图像理解**: 支持分析单张图片内容
- 🎬 **视频理解**: 支持通过图像序列分析视频内容
- 🌊 **流式输出**: 支持实时流式响应
- 📊 **详细日志**: 打印完整的HTTP请求体和响应信息
- 💰 **Token统计**: 显示详细的Token使用情况

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置API密钥

在运行程序之前，需要设置阿里云百炼API密钥：

```bash
export DASHSCOPE_API_KEY="your-api-key-here"
```

或者在Windows系统中：

```cmd
set DASHSCOPE_API_KEY=your-api-key-here
```

## 获取API密钥

1. 访问 [阿里云百炼控制台](https://bailian.console.aliyun.com/)
2. 登录您的阿里云账号
3. 在API管理页面获取您的API Key

## 运行演示

```bash
python qwen_vl_plus_demo.py
```

## 演示内容

程序包含三个主要演示：

### 1. 图像理解演示
- 分析单张图片内容
- 显示完整的HTTP请求和响应
- 统计Token使用情况

### 2. 视频理解演示
- 通过图像序列分析视频内容
- 支持多帧图像输入
- 理解视频的时序变化

### 3. 流式输出演示
- 实时流式响应
- 逐块显示生成内容
- 展示流式数据格式

## 输出示例

程序会打印详细的请求和响应信息：

```
================================================================================
📤 HTTP请求信息
================================================================================
方法: POST
URL: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
请求头:
  Authorization: Bearer sk-xxxxxxxx...xxxxxxxx
  Content-Type: application/json
请求体:
{
  "model": "qwen-vl-plus",
  "messages": [
    {
      "role": "user",
      "content": [
        {
          "type": "image_url",
          "image_url": {
            "url": "https://example.com/image.jpg"
          }
        },
        {
          "type": "text",
          "text": "这是什么？"
        }
      ]
    }
  ]
}

================================================================================
📥 HTTP响应信息
================================================================================
{
  "choices": [
    {
      "finish_reason": "stop",
      "message": {
        "role": "assistant",
        "content": "这是一张包含小狗和女孩的温馨照片..."
      }
    }
  ],
  "usage": {
    "input_tokens": 1234,
    "output_tokens": 567,
    "total_tokens": 1801,
    "image_tokens": 1000
  }
}
```

## 自定义使用

您可以修改代码中的图像URL和问题来测试不同的场景：

```python
# 自定义图像URL
image_url = "https://your-image-url.jpg"

# 自定义问题
question = "请详细描述这张图片的内容"

# 调用演示
demo.image_understanding_demo(image_url, question)
```

## 支持的模型

- qwen-vl-plus
- qwen-vl-max
- qwen-vl-max-latest
- 其他通义千问VL系列模型

## 注意事项

1. 确保您的API密钥有足够的额度
2. 图像URL必须是公开可访问的
3. 视频理解功能通过图像序列实现
4. 流式输出可能会产生更多的网络请求

## 错误处理

程序包含完整的错误处理机制：
- API密钥验证
- 网络请求异常处理
- 响应格式验证
- 用户中断处理

## 技术细节

- 使用OpenAI兼容的API接口
- 支持多种内容类型（文本、图像、视频）
- 实现了详细的请求/响应日志
- 提供了Token使用统计

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个演示程序。
