#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试流式响应的正确处理方法
"""

import os
import json
import requests
from pathlib import Path


def load_env_file():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        if key not in os.environ:
                            os.environ[key] = value
        except Exception as e:
            print(f"❌ 加载.env文件失败: {str(e)}")


def test_stream_request(request_data, test_name):
    """测试流式请求"""
    print(f"\n🧪 {test_name}")
    print("=" * 70)
    
    load_env_file()
    api_key = os.getenv('DASHSCOPE_API_KEY')
    base_url = os.getenv('DASHSCOPE_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
    
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("📤 请求体:")
    print(json.dumps(request_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, headers=headers, json=request_data, timeout=30, stream=True)
        print(f"\n📥 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 请求成功")
            
            # 处理流式响应
            print("🌊 流式响应内容:")
            print("-" * 50)
            
            full_content = ""
            chunk_count = 0
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    print(f"📦 Chunk {chunk_count + 1}: {line_str}")
                    
                    # 解析SSE格式
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        
                        if data_str.strip() == '[DONE]':
                            print("🏁 流式响应结束")
                            break
                        
                        try:
                            chunk_data = json.loads(data_str)
                            if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                delta = chunk_data['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    content = delta['content']
                                    full_content += content
                                    print(f"   💬 内容: {content}")
                        except json.JSONDecodeError:
                            print(f"   ⚠️ 无法解析JSON: {data_str}")
                    
                    chunk_count += 1
                    if chunk_count > 20:  # 限制显示的chunk数量
                        print("   ... (更多chunk)")
                        break
            
            print("-" * 50)
            print(f"📝 完整回答: {full_content}")
            return True
        else:
            print("❌ 请求失败")
            print(f"错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return False


def test_non_stream_request(request_data, test_name):
    """测试非流式请求"""
    print(f"\n🧪 {test_name}")
    print("=" * 70)
    
    # 移除流式配置
    request_data = request_data.copy()
    request_data["stream"] = False
    if "stream_options" in request_data:
        del request_data["stream_options"]
    
    load_env_file()
    api_key = os.getenv('DASHSCOPE_API_KEY')
    base_url = os.getenv('DASHSCOPE_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
    
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("📤 请求体:")
    print(json.dumps(request_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, headers=headers, json=request_data, timeout=30)
        print(f"\n📥 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 请求成功")
            response_data = response.json()
            
            if "choices" in response_data:
                content = response_data["choices"][0]["message"]["content"]
                print(f"🤖 回答: {content}")
                
                # 显示Token使用情况
                if "usage" in response_data:
                    usage = response_data["usage"]
                    print(f"📊 Token使用: 输入{usage.get('prompt_tokens', 0)} + 输出{usage.get('completion_tokens', 0)} = 总计{usage.get('total_tokens', 0)}")
            return True
        else:
            print("❌ 请求失败")
            print(f"错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return False


def main():
    """主函数"""
    print("🔍 qwen-vl-plus 流式响应测试")
    print("=" * 70)
    
    # 您的原始请求体
    original_request = {
        "max_tokens": 8192,
        "messages": [
            {
                "content": [
                    {
                        "text": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各 \r\n种任务。请根据用户的需求，提供准确、有用和友好的回答。",
                        "type": "text"
                    }
                ],
                "role": "system"
            },
            {
                "content": [
                    {
                        "text": "你好",
                        "type": "text"
                    }
                ],
                "role": "user"
            }
        ],
        "model": "qwen-vl-plus",
        "stream": True,
        "stream_options": {
            "include_usage": True
        },
        "tool_choice": "none",
        "tools": [
            {
                "function": {
                    "name": "NonInvocableTool"
                },
                "type": "function"
            }
        ]
    }
    
    # 测试1: 流式响应
    print("🌊 测试流式响应处理")
    test_stream_request(original_request, "原始请求体（流式）")
    
    # 测试2: 非流式响应
    print("\n📄 测试非流式响应处理")
    test_non_stream_request(original_request, "原始请求体（非流式）")
    
    print(f"\n🎯 结论:")
    print("如果流式响应成功，说明您的请求体格式是正确的！")
    print("之前的错误是因为没有正确处理流式响应格式。")


if __name__ == "__main__":
    main()
