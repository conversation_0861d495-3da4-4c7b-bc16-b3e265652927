#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复请求体问题的测试脚本
基于发现的问题进行针对性测试
"""

import os
import json
import requests
from pathlib import Path


def load_env_file(env_path=".env"):
    """加载.env文件中的环境变量"""
    env_file = Path(env_path)
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        if key not in os.environ:
                            os.environ[key] = value
        except Exception as e:
            print(f"❌ 加载.env文件失败: {str(e)}")


def test_request(request_data, test_name):
    """测试单个请求"""
    print(f"\n🧪 {test_name}")
    print("=" * 60)
    
    load_env_file()
    api_key = os.getenv('DASHSCOPE_API_KEY')
    base_url = os.getenv('DASHSCOPE_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
    
    if not api_key:
        print("❌ 未找到API密钥！")
        return False
    
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("📤 请求体:")
    print(json.dumps(request_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, headers=headers, json=request_data, timeout=30)
        print(f"\n📥 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 成功")
            return True
        else:
            print("❌ 失败")
            print(f"错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return False


def main():
    """主函数"""
    print("🔧 请求体问题修复测试")
    print("=" * 60)
    
    # 问题分析：
    # 1. 错误信息显示"The input messages do not contain elements with the role of user"
    # 2. 这说明API不能正确识别content数组格式的消息
    # 3. 需要测试不同的消息格式
    
    # 测试1: 原始问题请求（content数组格式）
    test1 = {
        "max_tokens": 8192,
        "messages": [
            {
                "content": [
                    {
                        "text": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各 \r\n种任务。请根据用户的需求，提供准确、有用和友好的回答。",
                        "type": "text"
                    }
                ],
                "role": "system"
            },
            {
                "content": [
                    {
                        "text": "你好",
                        "type": "text"
                    }
                ],
                "role": "user"
            }
        ],
        "model": "qwen-vl-ocr-latest"
    }
    
    # 测试2: 修复为标准字符串格式
    test2 = {
        "max_tokens": 8192,
        "messages": [
            {
                "role": "system",
                "content": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各 \r\n种任务。请根据用户的需求，提供准确、有用和友好的回答。"
            },
            {
                "role": "user",
                "content": "你好"
            }
        ],
        "model": "qwen-vl-ocr-latest"
    }
    
    # 测试3: 修复为标准字符串格式 + 清理换行符
    test3 = {
        "max_tokens": 8192,
        "messages": [
            {
                "role": "system",
                "content": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。"
            },
            {
                "role": "user",
                "content": "你好"
            }
        ],
        "model": "qwen-vl-ocr-latest"
    }
    
    # 测试4: 添加流式输出
    test4 = {
        "max_tokens": 8192,
        "messages": [
            {
                "role": "system",
                "content": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。"
            },
            {
                "role": "user",
                "content": "你好"
            }
        ],
        "model": "qwen-vl-ocr-latest",
        "stream": True,
        "stream_options": {
            "include_usage": True
        }
    }
    
    # 测试5: 添加tools配置
    test5 = {
        "max_tokens": 8192,
        "messages": [
            {
                "role": "system",
                "content": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。"
            },
            {
                "role": "user",
                "content": "你好"
            }
        ],
        "model": "qwen-vl-ocr-latest",
        "stream": True,
        "stream_options": {
            "include_usage": True
        },
        "tool_choice": "none",
        "tools": [
            {
                "function": {
                    "name": "NonInvocableTool",
                    "description": "A non-invocable tool for testing",
                    "parameters": {}
                },
                "type": "function"
            }
        ]
    }
    
    # 执行测试
    tests = [
        (test1, "原始问题请求（content数组格式）"),
        (test2, "修复为字符串格式（保留\\r\\n）"),
        (test3, "修复为字符串格式（清理\\r\\n）"),
        (test4, "添加流式输出"),
        (test5, "添加完整tools配置")
    ]
    
    results = []
    for test_data, test_name in tests:
        success = test_request(test_data, test_name)
        results.append((test_name, success))
        print("\n" + "-" * 60)
    
    # 总结
    print(f"\n🎯 测试总结:")
    print("=" * 60)
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n💡 结论:")
    print("主要问题是消息格式不正确：")
    print("❌ 错误格式: content: [{'text': '...', 'type': 'text'}]")
    print("✅ 正确格式: content: '...'")
    print("\n对于纯文本消息，应该使用字符串格式的content")
    print("只有在包含图像等多媒体内容时才使用数组格式")


if __name__ == "__main__":
    main()
