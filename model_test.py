#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同模型的兼容性
"""

import os
import json
import requests
from pathlib import Path


def load_env_file():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        if key not in os.environ:
                            os.environ[key] = value
        except Exception as e:
            print(f"❌ 加载.env文件失败: {str(e)}")


def test_model(model_name, request_data):
    """测试指定模型"""
    print(f"\n🧪 测试模型: {model_name}")
    print("=" * 50)
    
    load_env_file()
    api_key = os.getenv('DASHSCOPE_API_KEY')
    base_url = os.getenv('DASHSCOPE_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
    
    if not api_key:
        print("❌ 未找到API密钥！")
        return False
    
    # 设置模型
    request_data["model"] = model_name
    
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, headers=headers, json=request_data, timeout=30)
        print(f"📥 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 成功")
            response_data = response.json()
            if "choices" in response_data:
                content = response_data["choices"][0]["message"]["content"]
                print(f"🤖 回答: {content[:50]}...")
            return True
        else:
            print("❌ 失败")
            error_text = response.text
            if len(error_text) > 200:
                error_text = error_text[:200] + "..."
            print(f"错误: {error_text}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return False


def main():
    """主函数"""
    print("🔍 模型兼容性测试")
    print("=" * 50)
    
    # 基础请求体（最简单的格式）
    base_request = {
        "messages": [
            {
                "role": "system",
                "content": "你是一个智能助手。"
            },
            {
                "role": "user",
                "content": "你好"
            }
        ],
        "max_tokens": 100
    }
    
    # 测试不同的模型
    models_to_test = [
        "qwen-vl-ocr-latest",  # 您使用的模型
        "qwen-vl-plus",        # 我们之前成功的模型
        "qwen-plus",           # 纯文本模型
        "qwen-turbo"           # 另一个纯文本模型
    ]
    
    results = []
    for model in models_to_test:
        success = test_model(model, base_request.copy())
        results.append((model, success))
    
    print(f"\n🎯 测试总结:")
    print("=" * 50)
    for model, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {model}")
    
    # 如果qwen-vl-ocr-latest失败，测试正确的VL格式
    if not any(success for model, success in results if model == "qwen-vl-ocr-latest"):
        print(f"\n🔧 测试qwen-vl-ocr-latest的正确格式:")
        print("=" * 50)
        
        # VL模型需要图像输入
        vl_request = {
            "model": "qwen-vl-ocr-latest",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
                            }
                        },
                        {
                            "type": "text",
                            "text": "这是什么？"
                        }
                    ]
                }
            ],
            "max_tokens": 100
        }
        
        success = test_model("qwen-vl-ocr-latest (with image)", vl_request)
        if success:
            print("\n💡 发现问题：qwen-vl-ocr-latest是视觉模型，需要图像输入！")
            print("对于纯文本对话，应该使用qwen-plus或qwen-turbo模型")


if __name__ == "__main__":
    main()
