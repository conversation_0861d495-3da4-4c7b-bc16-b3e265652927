#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试qwen-vl-plus模型对不同请求体格式的支持
"""

import os
import json
import requests
from pathlib import Path


def load_env_file():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        if key not in os.environ:
                            os.environ[key] = value
        except Exception as e:
            print(f"❌ 加载.env文件失败: {str(e)}")


def test_request(request_data, test_name):
    """测试请求"""
    print(f"\n🧪 {test_name}")
    print("=" * 70)
    
    load_env_file()
    api_key = os.getenv('DASHSCOPE_API_KEY')
    base_url = os.getenv('DASHSCOPE_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
    
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("📤 请求体:")
    print(json.dumps(request_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, headers=headers, json=request_data, timeout=30)
        print(f"\n📥 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 成功")
            response_data = response.json()
            if "choices" in response_data:
                content = response_data["choices"][0]["message"]["content"]
                print(f"🤖 回答: {content[:100]}...")
                
                # 显示Token使用情况
                if "usage" in response_data:
                    usage = response_data["usage"]
                    print(f"📊 Token使用: 输入{usage.get('prompt_tokens', 0)} + 输出{usage.get('completion_tokens', 0)} = 总计{usage.get('total_tokens', 0)}")
            return True
        else:
            print("❌ 失败")
            error_text = response.text
            if len(error_text) > 300:
                error_text = error_text[:300] + "..."
            print(f"错误: {error_text}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return False


def main():
    """主函数"""
    print("🔍 qwen-vl-plus 模型测试")
    print("=" * 70)
    
    # 测试1: 您的原始失败请求体（包含\r\n）
    test1 = {
        "max_tokens": 8192,
        "messages": [
            {
                "content": [
                    {
                        "text": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各 \r\n种任务。请根据用户的需求，提供准确、有用和友好的回答。",
                        "type": "text"
                    }
                ],
                "role": "system"
            },
            {
                "content": [
                    {
                        "text": "你好",
                        "type": "text"
                    }
                ],
                "role": "user"
            }
        ],
        "model": "qwen-vl-plus",
        "stream": True,
        "stream_options": {
            "include_usage": True
        },
        "tool_choice": "none",
        "tools": [
            {
                "function": {
                    "name": "NonInvocableTool"
                },
                "type": "function"
            }
        ]
    }
    
    # 测试2: 清理换行符后的请求体
    test2 = {
        "max_tokens": 8192,
        "messages": [
            {
                "content": [
                    {
                        "text": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。",
                        "type": "text"
                    }
                ],
                "role": "system"
            },
            {
                "content": [
                    {
                        "text": "你好",
                        "type": "text"
                    }
                ],
                "role": "user"
            }
        ],
        "model": "qwen-vl-plus",
        "stream": True,
        "stream_options": {
            "include_usage": True
        },
        "tool_choice": "none",
        "tools": [
            {
                "function": {
                    "name": "NonInvocableTool"
                },
                "type": "function"
            }
        ]
    }
    
    # 测试3: 修复tools配置
    test3 = {
        "max_tokens": 8192,
        "messages": [
            {
                "content": [
                    {
                        "text": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。",
                        "type": "text"
                    }
                ],
                "role": "system"
            },
            {
                "content": [
                    {
                        "text": "你好",
                        "type": "text"
                    }
                ],
                "role": "user"
            }
        ],
        "model": "qwen-vl-plus",
        "stream": True,
        "stream_options": {
            "include_usage": True
        },
        "tool_choice": "none",
        "tools": [
            {
                "function": {
                    "name": "NonInvocableTool",
                    "description": "A non-invocable tool for testing",
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                },
                "type": "function"
            }
        ]
    }
    
    # 测试4: 移除tools配置
    test4 = {
        "max_tokens": 8192,
        "messages": [
            {
                "content": [
                    {
                        "text": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。",
                        "type": "text"
                    }
                ],
                "role": "system"
            },
            {
                "content": [
                    {
                        "text": "你好",
                        "type": "text"
                    }
                ],
                "role": "user"
            }
        ],
        "model": "qwen-vl-plus",
        "stream": True,
        "stream_options": {
            "include_usage": True
        }
    }
    
    # 测试5: 标准字符串格式对比
    test5 = {
        "max_tokens": 8192,
        "messages": [
            {
                "role": "system",
                "content": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。"
            },
            {
                "role": "user",
                "content": "你好"
            }
        ],
        "model": "qwen-vl-plus",
        "stream": True,
        "stream_options": {
            "include_usage": True
        }
    }
    
    tests = [
        (test1, "原始请求体（包含\\r\\n + tools）"),
        (test2, "清理\\r\\n后（保留tools）"),
        (test3, "完整tools配置"),
        (test4, "移除tools配置"),
        (test5, "标准字符串格式（对比）")
    ]
    
    results = []
    for test_data, test_name in tests:
        success = test_request(test_data, test_name)
        results.append((test_name, success))
        print("\n" + "-" * 70)
    
    # 总结
    print(f"\n🎯 qwen-vl-plus 测试总结:")
    print("=" * 70)
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    # 分析结果
    success_count = sum(1 for _, success in results if success)
    if success_count > 0:
        print(f"\n💡 发现:")
        print(f"qwen-vl-plus 模型支持 content 数组格式！")
        if success_count == len(results):
            print("✅ 所有格式都成功，说明 qwen-vl-plus 比 qwen-vl-ocr-latest 更灵活")
        else:
            print("⚠️ 部分格式成功，可能存在特定的配置要求")
    else:
        print(f"\n❌ qwen-vl-plus 也不支持这种格式，问题可能在其他地方")


if __name__ == "__main__":
    main()
