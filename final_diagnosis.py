#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终诊断：找出qwen-vl-ocr-latest模型的正确使用方法
"""

import os
import json
import requests
from pathlib import Path


def load_env_file():
    """加载.env文件"""
    env_file = Path(".env")
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        if key not in os.environ:
                            os.environ[key] = value
        except Exception as e:
            print(f"❌ 加载.env文件失败: {str(e)}")


def test_request(request_data, test_name):
    """测试请求"""
    print(f"\n🧪 {test_name}")
    print("=" * 60)
    
    load_env_file()
    api_key = os.getenv('DASHSCOPE_API_KEY')
    base_url = os.getenv('DASHSCOPE_BASE_URL', 'https://dashscope.aliyuncs.com/compatible-mode/v1')
    
    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    print("📤 请求体:")
    print(json.dumps(request_data, ensure_ascii=False, indent=2))
    
    try:
        response = requests.post(url, headers=headers, json=request_data, timeout=30)
        print(f"\n📥 状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 成功")
            response_data = response.json()
            if "choices" in response_data:
                content = response_data["choices"][0]["message"]["content"]
                print(f"🤖 回答: {content[:100]}...")
            return True
        else:
            print("❌ 失败")
            print(f"错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {str(e)}")
        return False


def main():
    """主函数"""
    print("🔍 qwen-vl-ocr-latest 模型诊断")
    print("=" * 60)
    
    print("📋 分析结果:")
    print("1. qwen-vl-ocr-latest 是OCR专用的视觉模型")
    print("2. 该模型要求输入必须包含图像")
    print("3. 纯文本对话会导致'no user role'错误")
    print("4. 需要使用正确的多模态消息格式")
    
    # 测试1: 正确的OCR格式（带图像）
    test1 = {
        "model": "qwen-vl-ocr-latest",
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
                        }
                    },
                    {
                        "type": "text",
                        "text": "请识别图片中的文字"
                    }
                ]
            }
        ],
        "max_tokens": 1000
    }
    
    # 测试2: 带系统提示词的OCR格式
    test2 = {
        "model": "qwen-vl-ocr-latest",
        "messages": [
            {
                "role": "system",
                "content": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。"
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
                        }
                    },
                    {
                        "type": "text",
                        "text": "请分析这张图片"
                    }
                ]
            }
        ],
        "max_tokens": 1000
    }
    
    # 测试3: 完整配置（包含tools等）
    test3 = {
        "model": "qwen-vl-ocr-latest",
        "messages": [
            {
                "role": "system",
                "content": "你是一个智能助手，能够帮助用户解答问题、提供信息和完成各种任务。请根据用户的需求，提供准确、有用和友好的回答。"
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": "https://dashscope.oss-cn-beijing.aliyuncs.com/images/dog_and_girl.jpeg"
                        }
                    },
                    {
                        "type": "text",
                        "text": "请分析这张图片"
                    }
                ]
            }
        ],
        "max_tokens": 8192,
        "stream": True,
        "stream_options": {
            "include_usage": True
        },
        "tool_choice": "none",
        "tools": [
            {
                "function": {
                    "name": "NonInvocableTool",
                    "description": "A non-invocable tool",
                    "parameters": {}
                },
                "type": "function"
            }
        ]
    }
    
    # 执行测试
    tests = [
        (test1, "基础OCR格式（仅图像+文本）"),
        (test2, "带系统提示词的OCR格式"),
        (test3, "完整配置的OCR格式")
    ]
    
    results = []
    for test_data, test_name in tests:
        success = test_request(test_data, test_name)
        results.append((test_name, success))
        print("\n" + "-" * 60)
    
    # 总结
    print(f"\n🎯 诊断总结:")
    print("=" * 60)
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    print(f"\n💡 问题根源:")
    print("您的原始请求失败的原因是：")
    print("1. ❌ qwen-vl-ocr-latest 是OCR专用模型，不支持纯文本对话")
    print("2. ❌ 该模型要求消息中必须包含图像内容")
    print("3. ❌ 使用了错误的content格式（数组格式用于多模态，字符串格式用于纯文本）")
    
    print(f"\n🔧 解决方案:")
    print("1. ✅ 如果要使用OCR功能，必须提供图像URL")
    print("2. ✅ 使用正确的多模态消息格式：content: [{'type': 'image_url', ...}, {'type': 'text', ...}]")
    print("3. ✅ 如果只需要文本对话，改用 qwen-plus 或 qwen-turbo 模型")
    
    print(f"\n📝 修正后的请求体示例:")
    print("```json")
    print(json.dumps(test2, ensure_ascii=False, indent=2))
    print("```")


if __name__ == "__main__":
    main()
